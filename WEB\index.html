<!DOCTYPE html>
<html lang="cs">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Seznam zaměstnanců OTE">
    <meta name="author" content="<PERSON>">
    <link rel="shortcut icon" href="img/favicon.ico" type="image/x-icon">
    <title>Seznam zaměstnanců</title>

    <!-- Preload kritických zdrojů -->
    <link rel="preload" href="img/logo1.svg" as="image">
    <link rel="preload" href="img/no-person-photo.png" as="image">
    <link rel="preload" href="css/style.css" as="style">
    <link rel="preload" href="css/directory.css" as="style">

    <link href="https://fonts.googleapis.com/css2?family=Nunito:wght@200;300;400;600;700;800;900&display=swap"
        rel="stylesheet">
    <script src="https://kit.fontawesome.com/14940b9e28.js" crossorigin="anonymous" onerror="console.log('FontAwesome kit se nepodařilo načíst')"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css" onerror="console.log('FontAwesome CSS se nepodařilo načíst')">
    <link rel="stylesheet" href="css/style.css" onerror="console.log('style.css se nepodařilo načíst')">
    <link rel="stylesheet" href="css/directory.css" onerror="console.log('directory.css se nepodařilo načíst')">


</head>

<body>

    <header id="headerNav">
        <div class="navbar">
            <div class="logo">
                <a href="index.html"><img src="img/logo1.svg" alt="Logo OTE" class="logo1"></a>
            </div>
            <h1 class="header-title">Seznam zaměstnanců</h1>
            <div class="search-container">
                <form action="#" class="search_form">
                    <input type="text" name="employee search" id="searchEmployee" placeholder="Vyhledat zaměstnance..." autocomplete="off" spellcheck="false">
                    <i class="fas fa-search" id="searchBtn"></i>
                    <div id="search-suggestions" class="search-suggestions"></div>
                </form>
            </div>
            <div class="header-info">
                <div class="employee-count-display" id="employee-count-display">
                    <i class="fas fa-users"></i>
                    <span id="employee-count-text">Načítání...</span>
                </div>
                <a href="mapa.html" target="_blank" class="map-link-header" title="Mapa rozmístění pracovišť">
                    <i class="fas fa-map-marked-alt"></i>
                    <span>Mapa rozmístění pracovišť</span>
                </a>
            </div>
            <div class="theme-switch-wrapper">
                <span class="theme-label">Tmavý režim</span>
                <label class="theme-switch" for="checkbox">
                    <input type="checkbox" id="checkbox" />
                    <div class="slider round">
                        <i class="fas fa-sun"></i>
                        <i class="fas fa-moon"></i>
                    </div>
                </label>
            </div>
        </div>
    </header>

    <section class="employee_directory">
        <div class="department-filters" id="department-filters">
        </div>

        <div class="employee_listing" id="employee-grid">
        </div>
    </section>

    <footer id="Footer">
        <p>Poslední aktualizace: <span id="lastUpdated"></span></p>
    </footer>

    <button id="backToTopBtn" title="Zpět nahoru">
        <i class="fas fa-chevron-up"></i>
        <span>Nahoru</span>
    </button>

    <div id="employeeModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <span class="close">&times;</span>
            </div>

            <div class="modal-body">
                <div class="modal-avatar-section">
                    <img id="modalImage" src="" alt="Employee Image">
                    <div class="modal-name-section">
                        <h2 id="modalName"></h2>
                        <div class="modal-badges">
                            <span id="modalPosition" class="position-badge"></span>
                            <span id="modalDepartment" class="department-badge"></span>
                        </div>
                    </div>
                </div>

                <div class="modal-info-section">
                    <div class="info-item">
                        <i class="fas fa-building"></i>
                        <span id="modalOffice">Pracoviště: Neuvedeno</span>
                    </div>
                    <div class="info-item">
                        <i class="fas fa-phone"></i>
                        <span id="modalPhone">Telefon: Neuvedeno</span>
                    </div>
                    <div class="info-item">
                        <i class="fas fa-mobile-alt"></i>
                        <span id="modalMobile">Mobil: Neuvedeno</span>
                    </div>
                    <div class="info-item">
                        <i class="fas fa-envelope"></i>
                        <span id="modalEmail">Email: Neuvedeno</span>
                    </div>
                </div>

                <div class="modal-description">
                    <p id="modalDescription"></p>
                </div>

                <div id="modalTeams" class="modal-actions-section"></div>
            </div>
        </div>
    </div>

    <script src="js/navbar.js" onerror="console.log('navbar.js se nepodařilo načíst')"></script>
    <script src="js/script.js" onerror="console.log('script.js se nepodařilo načíst')"></script>
    <script src="js/back-to-top.js" onerror="console.log('back-to-top.js se nepodařilo načíst')"></script>
    <script src="js/update-date.js" onerror="console.log('update-date.js se nepodařilo načíst')"></script>

    <script>
        document.addEventListener('contextmenu', function (e) {
            e.preventDefault();
        });

        // Error handling pro runtime chyby
        window.addEventListener('error', function(e) {
            console.log('Zachycena chyba:', e.error);
            return true; // Zabráníme zobrazení chyby v konzoli
        });

        // Handling pro unhandled promise rejections
        window.addEventListener('unhandledrejection', function(e) {
            console.log('Zachycena unhandled promise rejection:', e.reason);
            e.preventDefault();
        });

        // Suppress Chrome extension errors
        if (typeof chrome !== 'undefined' && chrome.runtime && chrome.runtime.onMessage) {
            chrome.runtime.onMessage.addListener(function(request, sender, sendResponse) {
                // Prázdný listener pro zabránění runtime.lastError
                return true;
            });
        }
    </script>

</body>

</html>
