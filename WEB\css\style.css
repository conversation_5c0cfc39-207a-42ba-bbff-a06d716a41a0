* {
    box-sizing: border-box;
    font-family: 'Nuni<PERSON>', sans-serif;
}

html {
    scroll-behavior: smooth;
}

body {
    background: #f8f9fa;
    font-family: 'Nunito', sans-serif;
    color: #222;
    margin: 0;
}

header {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(0, 173, 208, 0.1);
    box-shadow: 0 4px 32px rgba(0, 0, 0, 0.08);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.navbar {
    background: transparent;
    padding: 16px 32px;
    display: grid;
    grid-template-columns: auto 1fr auto auto auto;
    align-items: center;
    max-width: 1400px;
    margin: 0 auto;
    gap: 24px;
}

.navbar a {
    text-decoration: none;
    color: inherit;
    transition: all 0.3s ease;
}

.navbar .logo {
    flex: 0 0 auto;
    display: flex;
    align-items: center;
    gap: 16px;
}

.logo1 {
    height: 52px;
    width: auto;
    transition: transform 0.3s ease;
    filter: drop-shadow(0 2px 8px rgba(0, 173, 208, 0.15));
}

.logo1:hover {
    transform: scale(1.05);
}

.header-title {
    font-size: 1.75rem;
    font-weight: 700;
    color: #1e293b;
    margin: 0;
    letter-spacing: -0.025em;
    background: linear-gradient(135deg, #00add0 0%, #0891b2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.search-container {
    position: relative;
    min-width: 300px;
    max-width: 400px;
}

.search_form {
    position: relative;
    width: 100%;
}

#searchEmployee {
    width: 100%;
    padding: 14px 48px 14px 20px;
    border: 2px solid rgba(0, 173, 208, 0.1);
    border-radius: 16px;
    font-size: 15px;
    font-weight: 500;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow:
        0 1px 3px rgba(0, 0, 0, 0.05),
        0 0 0 1px rgba(0, 173, 208, 0.05);
}

#searchEmployee:focus {
    outline: none;
    border-color: #00add0;
    background: rgba(255, 255, 255, 1);
    box-shadow:
        0 8px 32px rgba(0, 173, 208, 0.15),
        0 0 0 3px rgba(0, 173, 208, 0.1);
    transform: translateY(-2px);
}

#searchEmployee::placeholder {
    color: #64748b;
    font-weight: 400;
    transition: all 0.3s ease;
}

#searchEmployee:focus::placeholder {
    color: #94a3b8;
    transform: translateX(4px);
}

#searchBtn {
    position: absolute;
    right: 16px;
    top: 50%;
    transform: translateY(-50%);
    color: #64748b;
    cursor: pointer;
    font-size: 16px;
    transition: all 0.3s ease;
    padding: 4px;
    border-radius: 8px;
}

#searchBtn:hover {
    color: #00add0;
    background: rgba(0, 173, 208, 0.1);
    transform: translateY(-50%) scale(1.1);
}

/* Theme Switch Styling */
.theme-switch-wrapper {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    transition: all 0.3s ease;
}

.theme-switch-wrapper:hover {
    background: rgba(255, 255, 255, 0.15);
    border-color: rgba(255, 255, 255, 0.3);
}

.theme-label {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.8);
    font-weight: 500;
    white-space: nowrap;
}

.theme-switch {
    position: relative;
    display: inline-block;
    width: 60px;
    height: 32px;
    cursor: pointer;
}

.theme-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    border-radius: 32px;
    box-shadow:
        0 2px 8px rgba(0, 0, 0, 0.1),
        inset 0 1px 2px rgba(255, 255, 255, 0.2);
}

.slider:before {
    position: absolute;
    content: "";
    height: 24px;
    width: 24px;
    left: 4px;
    bottom: 4px;
    background: white;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    border-radius: 50%;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.slider i {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    font-size: 12px;
    transition: all 0.3s ease;
}

.slider .fa-sun {
    left: 8px;
    color: #ffffff;
    opacity: 1;
}

.slider .fa-moon {
    right: 8px;
    color: #64748b;
    opacity: 0;
}

input:checked + .slider {
    background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
}

input:checked + .slider:before {
    transform: translateX(28px);
}

input:checked + .slider .fa-sun {
    opacity: 0;
    color: #64748b;
}

input:checked + .slider .fa-moon {
    opacity: 1;
    color: #ffffff;
}

.slider:hover {
    box-shadow:
        0 4px 16px rgba(0, 0, 0, 0.15),
        inset 0 1px 2px rgba(255, 255, 255, 0.2);
}

/* Header Info Panel */
.header-info {
    display: flex;
    align-items: center;
    gap: 12px;
}

.employee-count-display {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    background: rgba(0, 173, 208, 0.1);
    border: 1px solid rgba(0, 173, 208, 0.2);
    border-radius: 12px;
    color: #00add0;
    font-size: 14px;
    font-weight: 600;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: default;
}

.employee-count-display:hover {
    background: rgba(0, 173, 208, 0.15);
    border-color: rgba(0, 173, 208, 0.3);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 173, 208, 0.15);
}

.employee-count-display i {
    font-size: 16px;
}

.map-link-header {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    background: rgba(59, 130, 246, 0.1);
    border: 1px solid rgba(59, 130, 246, 0.2);
    border-radius: 12px;
    color: #3b82f6;
    text-decoration: none;
    font-size: 14px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.map-link-header:hover {
    background: rgba(59, 130, 246, 0.15);
    border-color: #3b82f6;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);
}

.map-link-header i {
    font-size: 16px;
}

/* Search Suggestions Styling */
.search-suggestions {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid rgba(0, 173, 208, 0.1);
    border-radius: 12px;
    box-shadow:
        0 8px 32px rgba(0, 0, 0, 0.1),
        0 0 0 1px rgba(0, 173, 208, 0.05);
    backdrop-filter: blur(10px);
    max-height: 300px;
    overflow-y: auto;
    z-index: 1001;
    margin-top: 8px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-8px);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.search-suggestions.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.search-suggestion-item {
    padding: 12px 16px;
    cursor: pointer;
    border-bottom: 1px solid rgba(0, 173, 208, 0.05);
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 12px;
}

.search-suggestion-item:last-child {
    border-bottom: none;
}

.search-suggestion-item:hover {
    background: rgba(0, 173, 208, 0.05);
    transform: translateX(4px);
}

.search-suggestion-item.highlighted {
    background: rgba(0, 173, 208, 0.1);
    color: #00add0;
}

.search-suggestion-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid rgba(0, 173, 208, 0.1);
}

.search-suggestion-info {
    flex: 1;
}

.search-suggestion-name {
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 2px;
}

.search-suggestion-department {
    font-size: 12px;
    color: #64748b;
}

@media screen and (min-width: 768px) {
    .navbar .menu_list {
        display: flex;
        margin-top: 10px;
        justify-content: space-between;
        width: 40%;
    }
    .navbar .dropdown_list {
        display: none;
    }
    .navbar .dropdown_link {
        display: flex;
    }
    .navbar .dropdown {
        position: relative;
    }
    .navbar .dropdown .dropdown_list {
        display: block;
        position: absolute;
        width: 300px;
        padding: 20px 20px 20px 80px;
        box-shadow: rgba(0, 0, 0, 0.1);
        background-color: #fff;
        top: 50px;
        right: 0;
    }
}

.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100vw;
    height: 100vh;
    overflow: auto;
    background: rgba(37,99,235,0.10);
    align-items: center;
    justify-content: center;
}

.modal-content {
    background: #ffffff;
    margin: 2% auto;
    padding: 0;
    border: none;
    border-radius: 20px;
    width: 95%;
    max-width: 420px;
    box-shadow:
        0 20px 50px rgba(0, 0, 0, 0.12),
        0 8px 20px rgba(0, 0, 0, 0.06);
    animation: modalFadeIn 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    overflow: hidden;
    backdrop-filter: blur(10px);
    position: relative;
}

@keyframes modalFadeIn {
    from { opacity: 0; transform: translateY(-50px); }
    to { opacity: 1; transform: translateY(0); }
}

.modal.closing .modal-content {
    animation: modalFadeOut 0.3s ease-in;
}

@keyframes modalFadeOut {
    from { opacity: 1; transform: translateY(0); }
    to { opacity: 0; transform: translateY(-50px); }
}

/* Modal Header */
.modal-header {
    display: flex;
    justify-content: flex-end;
    padding: 16px 20px 0;
}

/* Modal Body */
.modal-body {
    padding: 0 24px 24px;
}

/* Avatar Section */
.modal-avatar-section {
    display: flex;
    align-items: center;
    gap: 20px;
    margin-bottom: 24px;
    padding-bottom: 20px;
    border-bottom: 1px solid rgba(0, 173, 208, 0.1);
}

.modal-content img {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    object-fit: cover;
    border: 3px solid #00add0;
    transition: all 0.3s ease;
    flex-shrink: 0;
}

.modal-content img:hover {
    transform: scale(1.05);
    box-shadow: 0 8px 20px rgba(0, 173, 208, 0.3);
}

.modal-name-section {
    flex: 1;
}

.modal-content h2 {
    font-size: 1.5rem;
    font-weight: 700;
    color: #1e293b;
    margin: 0 0 8px 0;
    line-height: 1.2;
}

.modal-badges {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

/* Position and Department Badges */
.position-badge, .department-badge {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.position-badge {
    background: linear-gradient(135deg, #00add0, #0891b2);
    color: white;
}

.department-badge {
    background: rgba(0, 173, 208, 0.1);
    color: #00add0;
    border: 1px solid rgba(0, 173, 208, 0.2);
}

/* Info Section */
.modal-info-section {
    margin-bottom: 20px;
}

.info-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 0;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.info-item:last-child {
    border-bottom: none;
}

.info-item i {
    width: 20px;
    color: #00add0;
    font-size: 16px;
    text-align: center;
}

.info-item span {
    color: #64748b;
    font-size: 14px;
}

/* Description Section */
.modal-description {
    margin-bottom: 20px;
}

.modal-description p {
    color: #475569;
    line-height: 1.6;
    margin: 0;
    font-size: 14px;
}

.modal-info p {
    font-size: 16px;
    margin: 10px 0;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    gap: 10px;
}

.modal-info .icon {
    margin-right: 10px;
    color: #00add0;
    font-size: 20px;
}

.contact-item {
    display: flex;
    align-items: center;
    gap: 10px;
    margin: 10px 0;
    padding: 8px 12px;
    background-color: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #00add0;
    transition: background-color 0.3s ease;
}

.contact-item:hover {
    background-color: #e9ecef;
}

.contact-item .icon {
    color: #00add0;
    font-size: 18px;
    min-width: 20px;
}

.contact-item span {
    font-size: 16px;
    color: #333;
}


.modal-actions {
    display: flex;
    gap: 15px;
    margin-top: 25px;
    flex-wrap: wrap;
    justify-content: center;
}

.modal-action-btn {
    background: linear-gradient(135deg, #00808f 0%, #00add0 100%);
    color: white;
    border: none;
    border-radius: 12px;
    padding: 12px 20px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    text-decoration: none;
    box-shadow: 0 4px 15px rgba(0, 128, 143, 0.2);
}

.modal-action-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 128, 143, 0.3);
    background: linear-gradient(135deg, #005a66 0%, #008ca6 100%);
}

.modal-action-btn.map-btn {
    background: linear-gradient(135deg, #2563eb 0%, #3b82f6 100%);
    box-shadow: 0 4px 15px rgba(37, 99, 235, 0.2);
}

.modal-action-btn.map-btn:hover {
    background: linear-gradient(135deg, #1d4ed8 0%, #2563eb 100%);
    box-shadow: 0 8px 25px rgba(37, 99, 235, 0.3);
}

.modal-divider {
    width: 100%;
    height: 1px;
    background: linear-gradient(90deg, transparent 0%, rgba(0, 128, 143, 0.3) 50%, transparent 100%);
    margin: 20px 0;
}

.modal-badge {
    background: linear-gradient(135deg, #00808f 0%, #00add0 100%);
    color: white;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    margin-left: 10px;
    box-shadow: 0 2px 8px rgba(0, 128, 143, 0.2);
}

.close {
    color: #94a3b8;
    font-size: 24px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background: rgba(0, 0, 0, 0.05);
}

.close:hover,
.close:focus {
    color: #ef4444;
    background: rgba(239, 68, 68, 0.1);
    transform: scale(1.1);
}

#modalImage {
    width: 200px;
    height: 200px;
    object-fit: cover;
    border-radius: 50%;
    border: 5px solid #00808f;
    margin-bottom: 20px;
    box-shadow: 0 4px 10px rgba(0, 128, 143, 0.2);
}

#modalName {
    font-size: 28px;
    color: #00808f;
    margin-bottom: 10px;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
}

#modalPosition, #modalDepartment {
    font-size: 18px;
    margin: 15px 0;
    text-align: center;
    max-width: 80%;
    border: 1px solid #00add0;
    border-radius: 15px;
    background-color: #ffffff;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 15px;
    transition: background-color 0.3s ease, box-shadow 0.3s ease;
    padding: 15px 20px;
    font-weight: 600;
    color: #00808f;
}

#modalOffice {
    margin: 15px 0;
    text-align: center;
    width: 100%;
    display: flex;
    justify-content: center;
}

#modalPosition:hover, #modalDepartment:hover {
    background-color: #e0f7fa;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
}

#modalPosition::before {
    content: '💼';
    font-size: 24px;
    margin-right: 10px;
}

#modalDepartment::before {
    content: '🏢';
    font-size: 24px;
    margin-right: 10px;
}



#modalDescription {
    font-size: 16px;
    margin: 20px 0;
    text-align: center;
    max-width: 90%;
    border: 1px solid #00add0;
    border-radius: 10px;
    padding: 15px;
    background-color: #f0f8ff;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    color: #333;
    line-height: 1.5;
}

.modal-info {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 20px;
    border: 1px solid #00add0;
    border-radius: 10px;
    background-color: #f0f8ff;
    padding: 20px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    margin-top: 20px;
}

.modal-info p {
    font-size: 16px;
    margin: 10px 0;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
}

.modal-info .icon {
    margin-right: 10px;
    color: #00add0;
    font-size: 18px;
    font-size: 20px;
}

@media (max-width: 768px) {
    .modal-content {
        width: 98%;
        max-width: none;
        margin: 1% auto;
    }

    .modal-body {
        padding: 0 16px 16px;
    }

    .modal-avatar-section {
        flex-direction: column;
        text-align: center;
        gap: 16px;
    }

    .modal-content img {
        width: 100px;
        height: 100px;
    }

    .modal-badges {
        justify-content: center;
    }

    .modal-actions {
        flex-direction: column;
        gap: 8px;
    }

    .modal-action-btn {
        width: 100%;
    }
}

@media screen and (max-width: 768px) {
    .navbar {
        flex-direction: column;
        align-items: center;
        padding: 16px 20px;
        gap: 16px;
    }

    .navbar .logo {
        order: 1;
    }

    .header-title {
        order: 2;
        font-size: 1.5rem;
        text-align: center;
        margin: 8px 0;
    }

    .search-container {
        order: 3;
        width: 100%;
        max-width: none;
    }

    .header-info {
        order: 4;
        flex-direction: column;
        gap: 8px;
        width: 100%;
    }

    .employee-count-display,
    .map-link-header {
        justify-content: center;
        width: 100%;
    }

    .theme-switch-wrapper {
        order: 5;
        margin-top: 8px;
    }

    #searchEmployee {
        font-size: 16px; /* Prevents zoom on iOS */
    }
}

@media screen and (max-width: 480px) {
    .navbar {
        padding: 12px 16px;
    }

    .header-title {
        font-size: 1.25rem;
    }

    .logo1 {
        height: 40px;
    }

    .theme-switch {
        width: 50px;
        height: 26px;
    }

    .slider:before {
        height: 20px;
        width: 20px;
        left: 3px;
        bottom: 3px;
    }

    input:checked + .slider:before {
        transform: translateX(24px);
    }

    .header-info {
        gap: 6px;
    }

    .employee-count-display,
    .map-link-header {
        padding: 6px 12px;
        font-size: 13px;
    }

    .employee-count-display i,
    .map-link-header i {
        font-size: 14px;
    }
}

.search-button {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
}

#backToTopBtn {
    position: fixed;
    bottom: 32px;
    right: 32px;
    background: #2563eb;
    color: #fff;
    border: none;
    border-radius: 50px;
    padding: 10px 22px;
    font-size: 1.1rem;
    box-shadow: 0 2px 8px rgba(37,99,235,0.10);
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    z-index: 1001;
    transition: background 0.2s;
}

#backToTopBtn:hover {
    background: #1741a6;
}

#backToTopBtn i {
    font-size: 28px;
    margin-bottom: 7px;
}

#backToTopBtn span {
    font-size: 14px;
    font-weight: bold;
}

#backToTopBtn.show {
    right: -13px;
}

@media (max-width: 768px) {
    #backToTopBtn {
        padding: 14px 9px;
        font-size: 15px;
        width: 90px;
        right: -81px;
    }
    #backToTopBtn i {
        font-size: 25px;
    }
    #backToTopBtn span {
        font-size: 13px;
    }
    #backToTopBtn.show {
        right: -9px;
    }
}

#Footer {
    background: #fff;
    border-radius: 16px 16px 0 0;
    box-shadow: 0 -2px 12px rgba(0,0,0,0.06);
    padding: 12px 32px;
    text-align: right;
    color: #666;
    font-size: 0.98rem;
    margin-top: 32px;
}

#Footer p {
    margin: 0;
}

body.dark-mode {
    background-color: #1a1a1a;
    color: #f0f0f0;
}

.dark-mode header {
    background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
    border-bottom: 1px solid rgba(0, 173, 208, 0.2);
    box-shadow: 0 4px 32px rgba(0, 0, 0, 0.3);
}

.dark-mode .navbar a {
    color: #f1f5f9;
}

.dark-mode .header-title {
    background: linear-gradient(135deg, #00add0 0%, #0891b2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.dark-mode #searchEmployee {
    background: rgba(30, 41, 59, 0.8);
    color: #f1f5f9;
    border-color: rgba(0, 173, 208, 0.2);
    backdrop-filter: blur(10px);
}

.dark-mode #searchEmployee:focus {
    border-color: #00add0;
    background: rgba(30, 41, 59, 0.95);
    box-shadow:
        0 8px 32px rgba(0, 173, 208, 0.25),
        0 0 0 3px rgba(0, 173, 208, 0.15);
}

.dark-mode #searchEmployee::placeholder {
    color: #94a3b8;
}

.dark-mode #searchBtn {
    color: #94a3b8;
}

.dark-mode #searchBtn:hover {
    color: #00add0;
    background: rgba(0, 173, 208, 0.15);
}

.dark-mode .search-suggestions {
    background: rgba(30, 41, 59, 0.95);
    border-color: rgba(0, 173, 208, 0.2);
    backdrop-filter: blur(20px);
}

.dark-mode .search-suggestion-item {
    border-bottom-color: rgba(0, 173, 208, 0.1);
}

.dark-mode .search-suggestion-item:hover {
    background: rgba(0, 173, 208, 0.1);
}

.dark-mode .search-suggestion-name {
    color: #f1f5f9;
}

.dark-mode .search-suggestion-department {
    color: #94a3b8;
}

/* Dark mode pro header info */
.dark-mode .employee-count-display {
    background: rgba(0, 173, 208, 0.2);
    border-color: rgba(0, 173, 208, 0.3);
    color: #00add0;
}

.dark-mode .map-link-header {
    background: rgba(59, 130, 246, 0.2);
    border-color: rgba(59, 130, 246, 0.3);
    color: #60a5fa;
}

.dark-mode .map-link-header:hover {
    background: rgba(59, 130, 246, 0.25);
    border-color: #60a5fa;
}

/* Dark mode pro theme switch wrapper */
.dark-mode .theme-switch-wrapper {
    background: rgba(0, 0, 0, 0.2);
    border-color: rgba(255, 255, 255, 0.1);
}

.dark-mode .theme-switch-wrapper:hover {
    background: rgba(0, 0, 0, 0.3);
    border-color: rgba(255, 255, 255, 0.2);
}

.dark-mode .theme-label {
    color: rgba(255, 255, 255, 0.9);
}

/* Header scroll animation */
header.scrolled {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
}

.dark-mode header.scrolled {
    background: rgba(30, 41, 59, 0.95);
    backdrop-filter: blur(20px);
}

/* Loading animation for search */
.search-loading {
    position: relative;
}

.search-loading::after {
    content: '';
    position: absolute;
    right: 16px;
    top: 50%;
    transform: translateY(-50%);
    width: 16px;
    height: 16px;
    border: 2px solid #e2e8f0;
    border-top: 2px solid #00add0;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: translateY(-50%) rotate(0deg); }
    100% { transform: translateY(-50%) rotate(360deg); }
}

/* Smooth transitions for all interactive elements */
* {
    transition: color 0.2s ease, background-color 0.2s ease, border-color 0.2s ease, transform 0.2s ease, box-shadow 0.2s ease;
}

/* Focus styles for accessibility */
*:focus-visible {
    outline: 2px solid #00add0;
    outline-offset: 2px;
}

/* Improved button hover effects */
button, .filter-btn {
    position: relative;
    overflow: hidden;
}

button::before, .filter-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

button:hover::before, .filter-btn:hover::before {
    left: 100%;
}

.dark-mode .employee_listing .employee {
    background-color: #2a2a2a;
    box-shadow: 1px 1px 30px rgba(255, 255, 255, 0.1);
}

.dark-mode .employee_listing .employee h2 {
    color: #00add0;
}

.dark-mode .employee_listing .employee .job_type {
    background-color: #444;
}

.dark-mode .employee_listing .employee:hover,
.dark-mode .employee_listing .employee:hover h2 {
    color: #1a1a1a;
    background-color: #00add0;
}

.dark-mode .employee_listing .employee:hover .job_type {
    background-color: #1a1a1a;
    border: 1px solid #00add0;
}

.dark-mode #Footer {
    background-color: #2a2a2a;
    color: #00add0;
    border-top: 1px solid #444;
}

.dark-mode .modal-content {
    background: #1e293b;
    border: 1px solid rgba(0, 173, 208, 0.3);
    box-shadow:
        0 20px 50px rgba(0, 0, 0, 0.4),
        0 8px 20px rgba(0, 173, 208, 0.1);
}

.dark-mode .modal-avatar-section {
    border-bottom-color: rgba(0, 173, 208, 0.2);
}

.dark-mode .modal-content h2 {
    color: #f1f5f9;
}

.dark-mode .department-badge {
    background: rgba(0, 173, 208, 0.2);
    border-color: rgba(0, 173, 208, 0.3);
}

.dark-mode .info-item {
    border-bottom-color: rgba(255, 255, 255, 0.1);
}

.dark-mode .info-item span {
    color: #cbd5e1;
}

.dark-mode .modal-description p {
    color: #94a3b8;
}

.dark-mode .modal-actions-section {
    border-top-color: rgba(255, 255, 255, 0.1);
}

.dark-mode .close {
    background: rgba(255, 255, 255, 0.1);
    color: #cbd5e1;
}

.dark-mode .close:hover {
    background: rgba(239, 68, 68, 0.2);
    color: #ef4444;
}

.dark-mode #modalPosition,
.dark-mode #modalDepartment {
    background: rgba(30, 41, 59, 0.8);
    border-color: rgba(0, 173, 208, 0.3);
    color: #f1f5f9;
}

.dark-mode #modalPosition:hover,
.dark-mode #modalDepartment:hover {
    background: rgba(0, 173, 208, 0.2);
    border-color: #00add0;
}

.dark-mode #modalDescription {
    background: rgba(30, 41, 59, 0.6);
    border-color: rgba(0, 173, 208, 0.3);
    color: #cbd5e1;
}

.dark-mode .modal-info {
    background: rgba(30, 41, 59, 0.6);
    border-color: rgba(0, 173, 208, 0.3);
}

.dark-mode .contact-item {
    color: #cbd5e1;
}

.dark-mode .contact-item .icon {
    color: #00add0;
}

.dark-mode .close {
    color: #00add0;
}

.dark-mode .close:hover {
    color: #0891b2;
}

/* Modal Actions Section */
.modal-actions-section {
    border-top: 1px solid rgba(0, 0, 0, 0.05);
    padding-top: 20px;
}

.modal-actions {
    display: flex;
    gap: 12px;
    justify-content: center;
    flex-wrap: wrap;
}

.modal-action-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 24px;
    border-radius: 12px;
    font-weight: 600;
    font-size: 14px;
    text-decoration: none;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border: 2px solid transparent;
    position: relative;
    overflow: hidden;
    min-width: 140px;
    justify-content: center;
}

.modal-action-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.modal-action-btn:active {
    transform: translateY(0);
}

.modal-action-btn i {
    font-size: 16px;
}

/* Teams button */
.modal-action-btn:first-child {
    background: linear-gradient(135deg, #5b21b6 0%, #7c3aed 100%);
    color: white;
}

.modal-action-btn:first-child:hover {
    box-shadow: 0 8px 24px rgba(91, 33, 182, 0.3);
}

/* Email button */
.modal-action-btn:last-child {
    background: linear-gradient(135deg, #dc2626 0%, #ef4444 100%);
    color: white;
}

.modal-action-btn:last-child:hover {
    box-shadow: 0 8px 24px rgba(220, 38, 38, 0.3);
}

/* Dark mode pro action buttons */
.dark-mode .modal-action-btn {
    border-color: rgba(255, 255, 255, 0.1);
}

.dark-mode .modal-action-btn:hover {
    border-color: rgba(255, 255, 255, 0.3);
}

.dark-mode .close {
    color: #00add0;
}

.dark-mode .close:hover,
.dark-mode .close:focus {
    color: #008ca6;
}

.dark-mode #modalName {
    color: #00add0;
}

.dark-mode #modalPosition,
.dark-mode #modalDepartment {
    background-color: #333;
    color: #f0f0f0;
    border-color: #00add0;
}

.dark-mode #modalPosition:hover,
.dark-mode #modalDepartment:hover {
    background-color: #444;
}

.dark-mode #modalDescription {
    background-color: #333;
    color: #f0f0f0;
    border-color: #00add0;
}

.dark-mode .modal-info {
    background-color: #333;
    border-color: #00add0;
}

.dark-mode .modal-info p {
    color: #f0f0f0;
}

.dark-mode .modal-info .icon {
    color: #00add0;
}

.dark-mode .contact-item {
    background-color: #2a2a2a;
    border-left-color: #00add0;
}

.dark-mode .contact-item:hover {
    background-color: #333;
}

.dark-mode .contact-item span {
    color: #f0f0f0;
}

.dark-mode .modal-header {
    background: linear-gradient(135deg, #2a2a2a, #1a1a1a);
    border-color: #00add0;
}

.dark-mode .modal-info-group p {
    background-color: #333;
    border-color: #00add0;
    color: #f0f0f0;
}

.theme-switch-wrapper {
    margin-left: auto;
}

.theme-switch {
    display: flex;
    align-items: center;
    cursor: pointer;
}

.slider.round {
    background: #e0e7ef;
    border-radius: 16px;
    padding: 4px 12px;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 1.2rem;
    color: #2563eb;
}

.slider:before {
    background-color: #fff;
    bottom: 4px;
    content: "";
    height: 26px;
    left: 4px;
    position: absolute;
    transition: .4s;
    width: 26px;
    border-radius: 50%;
}

input:checked + .slider {
    background-color: #00add0;
}

input:checked + .slider:before {
    transform: translateX(26px);
}

.slider .fa-sun,
.slider .fa-moon {
    color: #ffffff;
    font-size: 16px;
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    transition: .4s;
}

.slider .fa-sun {
    left: 8px;
    opacity: 1;
}

.slider .fa-moon {
    right: 8px;
    opacity: 0;
}

input:checked + .slider .fa-sun {
    opacity: 0;
}

input:checked + .slider .fa-moon {
    opacity: 1;
}

.dark-mode .slider {
    background-color: #333;
}

.dark-mode .slider:before {
    background-color: #00add0;
}

.dark-mode #backToTopBtn {
    background-color: #00add0;
    color: #1a1a1a;
}

.dark-mode #backToTopBtn:hover {
    background-color: #008ca6;
}

.toggle-switch {
    position: relative;
    display: inline-block;
    width: 60px;
    height: 34px;
    background-color: #ccc;
    border-radius: 34px;
    border: 2px solid #666;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
}

.toggle-switch::before {
    content: '';
    position: absolute;
    width: 26px;
    height: 26px;
    border-radius: 50%;
    top: 2px;
    left: 2px;
    background-color: white;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
    transition: 0.4s;
}

.toggle-switch.active {
    background-color: #2196F3;
}

.toggle-switch.active::before {
    transform: translateX(26px);
}

.toggle-switch::after,
.toggle-switch.active::after {
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.icon {
    width: 24px;
    height: 24px;
    vertical-align: middle;
    margin-right: 8px;
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));
}

.modal-header {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 20px;
    padding: 1px;
    border: 1px solid #00add0;
    border-radius: 15px;
    background: linear-gradient(135deg, #f0f8ff, #e0f7fa);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    flex-wrap: wrap;
}

.modal-info-group {
    display: flex;
    justify-content: center;
    width: 100%;
    gap: 5px;
    flex-wrap: wrap;
}

.modal-info-group p {
    flex: 1 1 50%;
    max-width: 300px;
    text-align: center;
    margin: 0;
    padding: 1px;
    border: 1px solid #00add0;
    border-radius: 1px;
    background-color: #ffffff;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    word-wrap: break-word;
}

.modal-info-group p span {
    display: block;
    font-weight: bold;
    margin-bottom: 3px;
}

.modal-header:hover {
    transform: translateY(-5px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.2);
}


.employee_directory > .map-container, .employee_directory > img, .employee_directory > .office-map {
    flex: 1 1 0%;
    background: #fff;
    border-radius: 16px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.06);
    margin-left: 0;
    padding: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 0;
}
.employee_directory > img {
    max-width: 100%;
    height: auto;
    border-radius: 16px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.06);
}